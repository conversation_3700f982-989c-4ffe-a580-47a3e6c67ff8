import { MinusCircleFilled, PlusCircleFilled } from '@ant-design/icons';
import { Alert, Button, Input, Table, Form, Spin } from 'antd';
import FormBuilder from 'antd-form-builder';
import Column from 'antd/lib/table/Column';
import React, { Component, useEffect } from 'react';
import { generateUUID } from '../../../util/helpers';

const demoColMeta = [
    {
        key: 'srvc_type',
        label: 'Service type',
        widget: 'select',
        options: [
            {
                label: 'Homelane - Tickets',
                value: '13', // Service type id
            },
            {
                label: 'Homelane - Lead',
                value: '14', // Service type id
            },
            {
                label: 'Wify - Tickets',
                value: '15', // Service type id
            },
        ],
    },
    {
        key: 'sbtsk_status_key',
        label: 'Subtask Status',
        widget: 'select',
        options: [
            {
                label: '--Select--',
                value: '0',
            },
            {
                label: 'Assigned',
                value: 'kjbhvfkdf',
            },
            {
                label: 'Reached site',
                value: 'khbfgjhdw',
            },
            {
                label: 'Job complete',
                value: 'closed',
            },
        ],
    },
    {
        key: 'srvc_req_status',
        label: 'Service Request Status',
        widget: 'select',
        options: [
            {
                label: '--Select--',
                value: '',
            },
        ],
        dynamicMeta: (row, original_field_meta) => {
            let dynamicMetaRow = { ...original_field_meta };
            let statusesOptionsBySrvcTypeId = {
                15: [
                    {
                        label: 'Open',
                        value: 'open',
                    },
                    {
                        label: 'Scheduled',
                        value: 'ksdfbksldj',
                    },
                    {
                        label: 'Visited',
                        value: 'iuhdfljlk',
                    },
                    {
                        label: 'Closed',
                        value: 'closed',
                    },
                ],
                14: [
                    {
                        label: 'Open',
                        value: 'open',
                    },
                    {
                        label: 'Visited',
                        value: 'iuhdfljlk',
                    },
                    {
                        label: 'Closed',
                        value: 'closed',
                    },
                ],
                13: [
                    {
                        label: 'Open',
                        value: 'open',
                    },
                    {
                        label: 'Closed',
                        value: 'closed',
                    },
                ],
            };
            let optionsFrCurrSrvcTypeId =
                statusesOptionsBySrvcTypeId[row.srvc_type];
            if (optionsFrCurrSrvcTypeId) {
                dynamicMetaRow['options'] = [
                    ...dynamicMetaRow['options'],
                    ...optionsFrCurrSrvcTypeId,
                ];
            }
            // console.log('dynamicMeta original_field_meta',original_field_meta);
            // console.log('dynamicMeta row',row);
            return dynamicMetaRow;
        },
    },
];

let demoRowData = [
    {
        row_id: '13_kjbhvfkdf',
        srvc_type: '13',
        sbtsk_status_key: 'kjbhvfkdf',
    },
];

const CellFormField = ({ record, cellFieldMeta }) => {
    const [form] = Form.useForm();
    useEffect(() => {
        // console.log('record[cellFieldMeta.key]',record[cellFieldMeta.key])
        if (typeof record[cellFieldMeta.key] != 'object') {
            form.setFieldsValue(record);
        }
        // if()
    }, [record]);
    return (
        <Form initialValues={record} form={form}>
            <FormBuilder meta={cellFieldMeta} form={form} />
        </Form>
    );
};

export default class MetaInputTable extends Component {
    initState = {
        render_helper: false,
        render_refreshed: true, //making this true to stop component refreshing delay
        currentPage: 1,
        pageSize: 10,
        // visible: false,
        // isFormSubmitting: false,
        // viewData: undefined,
        // isLoadingViewData: false,
        // editMode : this.props.editMode,
        // error: ''
    };

    state = this.initState;

    constructor(props) {
        super(props);
    }

    getDataFrTable = () => {
        let rowData = this.props.demoMode ? demoRowData : this.props.rowData;
        rowData.map((singleRow, index) => {
            if (singleRow.input_table_id == undefined) {
                singleRow['input_table_id'] = generateUUID();
            }
            singleRow['key'] = singleRow['input_table_id'];
        });
        return rowData;
    };

    getColMeta = () => {
        let colMeta = this.props.demoMode ? demoColMeta : this.props.colMeta;
        return colMeta;
    };

    refresh = (tableRefresh) => {
        this.setState({
            render_helper: !this.state.render_helper,
            // render_refreshed: !tableRefresh, //removing this to stop component refreshing delay
        });
    };

    onEdittableControlClick(record, table_index, increment) {
        const newData = [...this.getDataFrTable()];
        let index = table_index;

        newData.forEach((singleRow, i) => {
            if (singleRow.input_table_id == record.input_table_id) {
                index = i;
            }
        });
        if (increment > 0) {
            // we need to add rows
            for (let i = 0; i < increment; i++) {
                newData.splice(index + 1, 0, {});
            }
        } else {
            // we need to delete current row
            if (this.getDataFrTable().length > 0) {
                // delete
                newData.splice(index, 1);
                // If newData is empty after deletion add an empty object
                if (newData.length === 0) {
                    newData.push({});
                }
            }
        }
        let { demoMode, onChange } = this.props;
        if (onChange) {
            onChange(newData);
        } else if (demoMode) {
            demoRowData = [...newData];
        }
        this.refresh(true);
    }

    handlePaginationChange = (page) => {
        this.setState({ currentPage: page });
    };

    render() {
        let { render_refreshed, currentPage, pageSize } = this.state;
        let { demoMode, onChange } = this.props;
        // if (!render_refreshed) {
        //     setTimeout(() => {
        //         this.setState({
        //             render_refreshed: true,
        //         });
        //     }, 100);
        // } //removing this to stop component refreshing delay
        return (
            <div className="gx-border-1 gx-border-red gx-py-1 table-responsive-wy">
                {demoMode && <Alert message="Running in DEMO MODE!!" />}
                {!render_refreshed && (
                    <div className="gx-text-center">
                        <Spin />
                    </div>
                )}
                {render_refreshed && (
                    <Table
                        // columns={columns}
                        pagination={{
                            pageSizeOptions: ['10', '50', '100', '200', '500'],
                            defaultPageSize: pageSize,
                            defaultCurrent: currentPage,
                            onChange: this.handlePaginationChange,
                        }}
                        // size='small'
                        dataSource={this.getDataFrTable()}
                        scroll={{ x: 'max-content' }} // Scroll Left to right at max content //
                        bordered={true}
                    >
                        {this.getColMeta().map((singleColFIeldMeta) => (
                            <Column
                                title={singleColFIeldMeta.label}
                                key={singleColFIeldMeta.key}
                                {...(this.props.noFilters ||
                                singleColFIeldMeta.disable_filter
                                    ? {}
                                    : {
                                          filters:
                                              singleColFIeldMeta.options.map(
                                                  (singleOption) => {
                                                      return {
                                                          ...singleOption,
                                                          text: singleOption.label,
                                                      };
                                                  }
                                              ),
                                          filterSearch: true,
                                          onFilter: (value, record) =>
                                              record[singleColFIeldMeta.key] ==
                                              value,
                                      })}
                                render={(text, record, index) => {
                                    let cellFieldMeta;
                                    if (singleColFIeldMeta.dynamicMeta) {
                                        cellFieldMeta =
                                            singleColFIeldMeta.dynamicMeta(
                                                record,
                                                singleColFIeldMeta
                                            );
                                    } else {
                                        cellFieldMeta = {
                                            ...singleColFIeldMeta,
                                        };
                                    }
                                    let original_key = cellFieldMeta.key;
                                    cellFieldMeta.label = null;
                                    cellFieldMeta.onChange = (value) => {
                                        record[original_key] =
                                            value?.target?.value || value;
                                        if (onChange) {
                                            onChange(this.getDataFrTable());
                                        }
                                        this.refresh();
                                    };
                                    if (this.props.readOnly) {
                                        cellFieldMeta.widgetProps = {
                                            ...(cellFieldMeta.widgetProps ||
                                                {}),
                                            disabled: true,
                                        };
                                    }

                                    // console.log('row index',index)
                                    // console.log('cellFieldMeta',cellFieldMeta)
                                    // console.log('record',record)

                                    let duplicateRecord = { ...record };
                                    if (record[original_key]) {
                                        duplicateRecord[cellFieldMeta.key] =
                                            record[original_key];
                                    }
                                    return (
                                        <div className="gx-px-2">
                                            <CellFormField
                                                cellFieldMeta={cellFieldMeta}
                                                record={duplicateRecord}
                                            />
                                        </div>
                                    );
                                }}
                            />
                        ))}
                        {!this.props.hideActionBtn &&
                            this.props.edittable &&
                            !this.props.readOnly && (
                                <Column
                                    key="edittable_controls"
                                    title="Actions"
                                    fixed={
                                        this.props.actionColFixed
                                            ? 'right'
                                            : undefined
                                    }
                                    width={110}
                                    render={(text, record, index) => {
                                        return (
                                            <div>
                                                <Button
                                                    type="link"
                                                    className="gx-m-0 gx-text-success"
                                                    icon={<PlusCircleFilled />}
                                                    onClick={() =>
                                                        this.onEdittableControlClick(
                                                            record,
                                                            index,
                                                            1
                                                        )
                                                    }
                                                />
                                                <Button
                                                    type="link"
                                                    className="gx-m-0 gx-text-danger"
                                                    icon={<MinusCircleFilled />}
                                                    onClick={() =>
                                                        this.onEdittableControlClick(
                                                            record,
                                                            index,
                                                            -1
                                                        )
                                                    }
                                                />
                                            </div>
                                        );
                                    }}
                                />
                            )}
                    </Table>
                )}

                {demoMode && (
                    <div className="gx-border gx-border-blue gx-p-1 gx-mt-3">
                        {JSON.stringify(this.getDataFrTable())}
                    </div>
                )}
            </div>
        );
    }
}
