var express = require('express');
var router = express.Router();
var { getUserContextFrmReq } = require('../../../api_models/utils/authrizor');

//proto route
router.get('/proto/:entry_id?', function (req, res, next) {
    console.log('calllllllllllll');
    const entry_id = req.params.entry_id;
    const model = setParamsToModel(req);
    model.getViewData(req.query, entry_id).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});
//proto with brandid
router.get('/proto/:entry_id/:brand_id', function (req, res, next) {
    console.log('calllllllllllll');
    const entry_id = req.params.entry_id;
    const brand_id = req.params.brand_id;
    const model = setParamsToModel(req);
    model.getViewData(req.query, entry_id, brand_id).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

// Get single entry for prefill (proto with single entry_id)
router.get('/proto/:entry_id', function (req, res, next) {
    const entry_id = req.params.entry_id;
    const model = setParamsToModel(req);
    model.getSingleEntry(req.query, entry_id).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

// Get execution master data by vertical and brand for prefill
router.get('/by-vertical-brand', function (req, res, next) {
    const model = setParamsToModel(req);
    model.getByVerticalAndBrand(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});
//post route
router.post('/', function (req, res, next) {
    console.log('calllllllllllll');
    console.log('req.body=>>>>>>>>', req.body);
    const model = setParamsToModel(req);
    model.createOrUpdate(req.body).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});
// router.post('/proto/:entry_id', function (req, res, next) {
//     console.log('calllllllllllll')
//     console.log('req.body', req.body);
//     const entry_id = req.params.entry_id;
//     const model = setParamsToModel(req);
//     model.getBrandAndSrvcTypeList(req.body, entry_id).then((operationResp) => {
//         res.status(operationResp.httpStatus).send(operationResp.resp);
//     });
// });

const setParamsToModel = (req) => {
    const model = require('../../../api_models/setup/execution/execution_master_model');
    model.database = req.app.get('db');
    model.ip_addr = req.ip;
    model.user_agent = req.get('User-Agent');
    model.user_context = getUserContextFrmReq(req);
    return model.getFreshInstance(model);
};

module.exports = router;
