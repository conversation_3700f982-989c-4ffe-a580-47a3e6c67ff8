import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Col,
    <PERSON>lapse,
    Form,
    Modal,
    Row,
    Spin,
    message,
} from 'antd';
import FormBuilder from 'antd-form-builder';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import http_utils from '../../../../util/http_utils';
import CircularProgress from '../../../../components/CircularProgress';
import { UploadOutlined } from '@ant-design/icons';
import BulkUploader from '../../../../components/wify-utils/BulkUploader';
import { FaLaptopHouse } from 'react-icons/fa';
import {
    convertDateFieldsToMoments,
    convertUTCToDisplayTime,
    hasAnyFileChanged,
} from '../../../../util/helpers';
import UserName from '../../../../components/wify-utils/UserName';
import {
    decodeCameraSectionsFrm<PERSON>son,
    decodeFieldsMetaFrm<PERSON>son,
    decodeFileSectionsFrm<PERSON>son,
    decodeMicSectionsFrmJson,
} from '../../../../components/wify-utils/FieldCreator/helpers';
import S3Uploader from '../../../../components/wify-utils/S3Uploader/S3Uploader';
import CameraInput from '../../../../components/wify-utils/CameraInput';
import MicInputV2 from '../../../../components/wify-utils/MicInput_v2';
const protoUrl = '/parts_management/products/proto';
const submitUrl = '/parts_management/products';
const ItemEditor = ({
    itemEditorData,
    showEditor,
    onClose,
    editMode,
    onChange,
    configData,
    readOnly,
}) => {
    const [form] = Form.useForm();
    const forceUpdate = FormBuilder.useForceUpdate();
    const initialValues = itemEditorData || {};
    const [viewData, setviewData] = useState(undefined);
    const [error, setError] = useState();
    const [isFormSubmitting, setIsFormSubmitting] = useState(false);
    const [isLoadingViewData, setIsLoadingViewData] = useState(false);
    const [fileSections, setFileSections] = useState([]);
    const [filesBySection, setFileBySections] = useState({});
    const [micSections, setMicSections] = useState([]);
    const [micRecordingsBySection, setMicRecordingBySections] = useState({});
    const [cameraSections, setCameraSections] = useState([]);
    const [cameraRecordingsBySection, setCameraRecordingBySections] = useState(
        {}
    );
    const [sectionWiseUploaderReady, setSectionWiseUploaderReady] = useState(
        {}
    );
    const [sectionWiseMicUploaderReady, setSectionWiseMicUploaderReady] =
        useState({});
    const [sectionWiseCameraUploaderReady, setSectionWiseCameraUploaderReady] =
        useState({});

    const onFormValueChange = (changedValues, allValues) => {
        forceUpdate();
    };

    useEffect(() => {
        initViewData();
    }, []);

    const initViewData = () => {
        if (itemEditorData || viewData === undefined) {
            setIsLoadingViewData(true);
            let params = {};

            const url =
                protoUrl + '/' + (editMode ? itemEditorData?.product_id : 0);

            const onComplete = (resp) => {
                initConfigData(resp, () => {
                    setviewData(resp.data);
                    setIsLoadingViewData(false);
                });
            };

            const onError = (error) => {
                // console.log(error.response.status);
                setError(http_utils.decodeErrorToMessage(error));
                setIsLoadingViewData(false);
            };

            http_utils.performGetCall(url, params, onComplete, onError);
        }
    };
    const initConfigData = (
        entry_specific_data,
        then,
        dont_override_files = false
    ) => {
        let newFileSections = [];
        let custom_file_sections = getCustomFileSectionsFrmConfig();
        if (custom_file_sections && custom_file_sections.length > 0) {
            newFileSections = [...custom_file_sections, ...newFileSections];
        }
        // Prefilling attachments
        let initialFilesBySection = dont_override_files ? filesBySection : {};

        if (entry_specific_data.data?.form_data) {
            initialFilesBySection =
                entry_specific_data.data.form_data['attachments'];
        }

        let newMicSections = [];
        let customMicSections = getCustomMicSectionsFrmConfig();
        if (customMicSections && customMicSections.length > 0) {
            newMicSections = [...newMicSections, ...customMicSections];
        }

        let initialMicRecordingsBySection = dont_override_files
            ? micRecordingsBySection
            : {};

        if (entry_specific_data.data?.form_data) {
            initialMicRecordingsBySection =
                entry_specific_data.data.form_data['mic_files'];
        }

        let newCameraSections = [];
        let customCameraSections = getCustomCameraSectionsFrmConfig();
        if (customCameraSections && customCameraSections.length > 0) {
            newCameraSections = [...newCameraSections, ...customCameraSections];
        }

        let initialCameraRecordingsBySection = dont_override_files
            ? cameraRecordingsBySection
            : {};
        if (entry_specific_data.data?.form_data) {
            initialCameraRecordingsBySection =
                entry_specific_data.data.form_data['camera_files'];
        }

        setFileSections(newFileSections);
        setFileBySections({ ...initialFilesBySection });
        setMicSections(newMicSections);
        setMicRecordingBySections({ ...initialMicRecordingsBySection });
        setCameraSections(newCameraSections);
        setCameraRecordingBySections({ ...initialCameraRecordingsBySection });
        then();
    };

    const submitForm = (data) => {
        setIsFormSubmitting(true);
        let params = data;
        if (editMode) {
            let isAnyFileChanged = hasAnyFileChanged(
                filesBySection,
                viewData?.form_data?.attachments
            );
            if (isAnyFileChanged) {
                params['attachments'] = filesBySection;
            }
            // compare files with prefill data
            let isAnyMicFileChanged = hasAnyFileChanged(
                micRecordingsBySection,
                viewData?.form_data?.mic_files
            );
            if (isAnyMicFileChanged) {
                params['mic_files'] = micRecordingsBySection;
            }
            //compare camera files
            let isAnyCameraFileChanged = hasAnyFileChanged(
                cameraRecordingsBySection,
                viewData?.form_data?.camera_files
            );
            if (isAnyCameraFileChanged) {
                params['camera_files'] = cameraRecordingsBySection;
            }
        } else {
            params['attachments'] = filesBySection;
            params['mic_files'] = micRecordingsBySection;
            params['camera_files'] = cameraRecordingsBySection;
        }

        console.log('params', params);
        const onComplete = (resp) => {
            // then set loading false
            setIsFormSubmitting(false);
            if (onClose) {
                onClose();
            }
            if (onChange) {
                onChange();
            }
        };
        const onError = (error) => {
            // console.log(error.response.status);
            setError(http_utils.decodeErrorToMessage(error));
            setIsFormSubmitting(false);
        };
        let url = submitUrl;
        // console.log("rating submit url",url);
        if (editMode) {
            http_utils.performPutCall(
                url + '/' + itemEditorData?.product_id,
                params,
                onComplete,
                onError
            );
        } else {
            http_utils.performPostCall(url, params, onComplete, onError);
        }
    };
    const getCustomFieldsJson = () => {
        return configData?.sku_custom_fields;
    };

    const getCustomFieldMeta = () => {
        let customFields =
            decodeFieldsMetaFrmJson(
                getCustomFieldsJson(),
                4,
                false,
                true,
                form
            ) || [];
        if (customFields?.length > 0) {
            return customFields.map((singleCustomField) => {
                singleCustomField['colSpan'] = singleCustomField.colSpan || 2;
                return singleCustomField;
            });
        }
        return customFields;
    };
    const getActiveStatusMeta = () => {
        return {
            columns: 4,
            formItemLayout: null,
            fields: [
                {
                    key: 'is_active',
                    label: 'Active',
                    colSpan: 4,
                    widget: 'switch',
                    initialValue: viewData?.is_active || true,
                },
            ],
        };
    };
    const meta = () => {
        const activeCategoryList = [];
        if (
            viewData?.filters_proto?.category_list &&
            viewData?.filters_proto?.category_list.length > 0
        ) {
            viewData.filters_proto.category_list.forEach((singleCategory) => {
                if (singleCategory.is_active) {
                    activeCategoryList.push(singleCategory);
                }
            });
        }
        return {
            columns: 4,
            formItemLayout: null,
            fields: [
                {
                    key: 'product_details',
                    colSpan: 4,
                    render: () => (
                        <div className="gx-mt-2">
                            <b>Product Details</b>
                            <hr></hr>
                        </div>
                    ),
                },
                {
                    key: 'sku_code',
                    label: 'SKU Code',
                    colSpan: 2,
                    placeholder: 'Eg: SKU-101',
                    required: true,
                    disabled: editMode,
                    rules: [
                        {
                            max: 20,
                            message: 'Spare code must be max 20 characters.',
                        },
                        {
                            validator: (_, value) => {
                                if (!value) {
                                    // If value is empty, clear all validation errors
                                    return Promise.resolve();
                                }
                                const skuCodePattern = /^[^\s]+$/;
                                if (!skuCodePattern.test(value)) {
                                    return Promise.reject(
                                        new Error(
                                            'SKU Code cannot contain spaces'
                                        )
                                    );
                                }

                                return Promise.resolve();
                            },
                        },
                    ],
                },
                {
                    key: 'product_name',
                    label: 'Product name',
                    required: true,
                    colSpan: 2,
                    placeholder: 'Eg: Smart Ceiling fan',
                },
                   {
                    key: 'service_type',
                    label: 'Service Type',
                    colSpan: 2,
                    placeholder: 'Select Service Type',
                    widget: 'select',
                    options: viewData?.filters_proto?.service_type_list || [],
                    widgetProps: {
                        mode: 'multiple',
                        optionFilterProp: 'children',
                    },
                },
                {
                    key: 'category_id',
                    label: 'Category',
                    colSpan: 2,
                    placeholder: 'Select category',
                    widget: 'select',
                    options: !editMode
                        ? activeCategoryList
                        : viewData.filters_proto?.category_list,
                    widgetProps: {
                        mode: 'single',
                        optionFilterProp: 'children',
                    },
                },
                {
                    label: 'Does have serial number',
                    key: 'does_has_serial_no',
                    initialValue: false,
                    colSpan: 4,
                    disabled: editMode,
                    widget: 'checkbox',
                },
                {
                    key: 'product_description',
                    label: 'Description',
                    colSpan: 4,
                    widget: 'textarea',
                },
                {
                    key: 'price',
                    required: true,
                    label: 'Price/Unit',
                    colSpan: 4,
                    widget: 'number',
                    rules: [
                        {
                            type: 'number',
                            min: 0,
                            message: 'Number must be 0 or greater',
                        },
                    ],
                },
                ...getCustomFieldMeta(),
            ],
        };
    };

    const getCustomFileSectionsFrmConfig = () => {
        let customFileSections = decodeFileSectionsFrmJson(
            getCustomFieldsJson()
        );
        return customFileSections;
    };

    const getCustomMicSectionsFrmConfig = () => {
        return decodeMicSectionsFrmJson(getCustomFieldsJson());
    };

    const getCustomCameraSectionsFrmConfig = () => {
        return decodeCameraSectionsFrmJson(getCustomFieldsJson());
    };

    const onFilesChanged = (section, files) => {
        let newFilesBySection = filesBySection;
        newFilesBySection[section] = files;
        setFileBySections(newFilesBySection);
    };

    const onMicFilesChanged = (section, files) => {
        let newFilesBySection = micRecordingsBySection;
        newFilesBySection[section] = files;
        setMicRecordingBySections(newFilesBySection);
    };

    const onCameraFilesChanged = (section, files) => {
        let newFilesBySection = cameraRecordingsBySection;
        newFilesBySection[section] = files;
        setCameraRecordingBySections(newFilesBySection);
    };

    const onFileUploaderReadyChange = (section, isReady) => {
        let newSectionWiseReady = sectionWiseUploaderReady;
        newSectionWiseReady[section] = isReady;
        setSectionWiseUploaderReady(newSectionWiseReady);
    };

    const onMicFileUploaderReadyChange = (section, isReady) => {
        let newSectionWiseReady = sectionWiseMicUploaderReady;
        newSectionWiseReady[section] = isReady;
        setSectionWiseMicUploaderReady(newSectionWiseReady);
    };

    const onCameraFileUploaderReadyChange = (section, isReady) => {
        let newSectionWiseReady = sectionWiseCameraUploaderReady;
        newSectionWiseReady[section] = isReady;
        setSectionWiseCameraUploaderReady(newSectionWiseReady);
    };

    const getInitialValues = () => {
        let prefillFormData = { ...viewData?.form_data, ...viewData };
        if (prefillFormData) {
            prefillFormData = convertDateFieldsToMoments(
                prefillFormData,
                meta()?.fields
            );
        }
        return prefillFormData;
    };

    const getDescriptionFrAlert = () => {
        return (
            <p>
                {viewData?.is_active ? (
                    <>
                        {viewData?.last_u_date ? (
                            <>
                                Last Updated -{' '}
                                <b>
                                    {convertUTCToDisplayTime(
                                        viewData?.last_u_date
                                    )}
                                </b>
                                , by {getUserNameWhoActivateandDeactivatesYou()}
                                {<br></br>}
                                Created on -{' '}
                                <b>
                                    {convertUTCToDisplayTime(
                                        viewData?.created_on
                                    )}
                                </b>
                                {getUserNameWhoCreatedYou() != null
                                    ? getUserNameWhoCreatedYou()
                                    : ''}
                            </>
                        ) : (
                            <>
                                Created on -{' '}
                                <b>
                                    {convertUTCToDisplayTime(
                                        viewData?.created_on
                                    )}
                                </b>
                                {getUserNameWhoCreatedYou() != null
                                    ? getUserNameWhoCreatedYou()
                                    : ''}
                            </>
                        )}
                    </>
                ) : (
                    <>
                        {viewData?.last_u_date ? (
                            <>
                                Inactive from -{' '}
                                <b>
                                    {convertUTCToDisplayTime(
                                        viewData?.last_u_date
                                    )}
                                </b>
                                , by {getUserNameWhoActivateandDeactivatesYou()}
                                {<br></br>}
                                Created on -{' '}
                                <b>
                                    {convertUTCToDisplayTime(
                                        viewData?.created_on
                                    )}
                                </b>
                                {getUserNameWhoCreatedYou() != null
                                    ? getUserNameWhoCreatedYou()
                                    : ''}
                            </>
                        ) : (
                            <>
                                Created on -{' '}
                                <b>
                                    {convertUTCToDisplayTime(
                                        viewData?.created_on
                                    )}
                                </b>
                                {getUserNameWhoCreatedYou() != null
                                    ? getUserNameWhoCreatedYou()
                                    : ''}
                            </>
                        )}
                    </>
                )}
            </p>
        );
    };

    const getUserNameWhoActivateandDeactivatesYou = () => {
        return <UserName id={viewData?.is_active_state_change_by} />;
    };

    const getUserNameWhoCreatedYou = () => {
        return <UserName id={viewData?.c_by} prefix={', by'} />;
    };

    const tellParentToRefreshList = () => {
        if (onChange) {
            onChange();
        }
    };

    const getFieldsMetaFrBulkUpload = () => {
        return [...meta().fields];
    };
    return showEditor ? (
        <Modal
            title={editMode ? 'Edit' : 'Add Product'}
            visible={showEditor}
            confirmLoading={isFormSubmitting}
            onCancel={onClose}
            width={800}
            style={{
                marginTop: '-70px',
            }}
            bodyStyle={{
                minHeight: '85vh',
                padding: '18px',
                paddingTop: '0px',
            }}
            footer={false}
        >
            {isLoadingViewData ? (
                <div className="gx-loader-view gx-loader-position">
                    <CircularProgress />
                </div>
            ) : viewData == undefined && editMode ? (
                <p className="gx-text-red">{error}</p>
            ) : (
                <Row>
                    {!editMode && (
                        <Col xs={24} className="gx-my-1">
                            <Collapse>
                                <Collapse.Panel
                                    header={
                                        <span className="gx-text-primary">
                                            <UploadOutlined className="gx-mr-2" />
                                            Click here for Bulk creation
                                        </span>
                                    }
                                >
                                    <div>
                                        <BulkUploader
                                            // demoMode
                                            // renderFormsForRows
                                            // debugMode
                                            onDataModified={(entry_ids) =>
                                                tellParentToRefreshList(0)
                                            }
                                            submitUrl={submitUrl}
                                            dataProto={getFieldsMetaFrBulkUpload()}
                                            // timeFormatMsg
                                        />
                                    </div>
                                </Collapse.Panel>
                            </Collapse>
                        </Col>
                    )}
                    <Col xs={24}>
                        <Form
                            className="gx-w-100"
                            layout="vertical"
                            initialValues={getInitialValues()}
                            ref={form}
                            onFinish={(data) => {
                                submitForm(data);
                            }}
                            onValuesChange={onFormValueChange}
                        >
                            <FormBuilder
                                form={form}
                                meta={meta()}
                                disabled={readOnly}
                            />
                            {micSections.map((singleMicSection, index) => (
                                <Col
                                    xs={24}
                                    md={24}
                                    className="gx-pl-0"
                                    key={singleMicSection.key}
                                >
                                    {singleMicSection.title != '' && (
                                        <h3 className="gx-mt-3">
                                            {singleMicSection.title}
                                            <hr className="gx-bg-dark"></hr>
                                        </h3>
                                    )}
                                    <MicInputV2
                                        authToken={http_utils.getAuthToken()}
                                        prefixDomain={http_utils.getCDNDomain()}
                                        initialFiles={
                                            editMode
                                                ? getInitialValues()
                                                      .mic_files?.[
                                                      singleMicSection.key
                                                  ]
                                                : []
                                        }
                                        onFilesChanged={(files) => {
                                            onMicFilesChanged(
                                                singleMicSection.key,
                                                files
                                            );
                                        }}
                                        onReadyStatusChange={(isReady) => {
                                            onMicFileUploaderReadyChange(
                                                singleMicSection.key,
                                                isReady
                                            );
                                        }}
                                        readOnly={singleMicSection.disabled}
                                    />
                                </Col>
                            ))}
                            {cameraSections.map(
                                (singleCameraSection, index) => (
                                    <Col
                                        xs={24}
                                        md={24}
                                        className="gx-pl-0"
                                        key={singleCameraSection.key}
                                    >
                                        {singleCameraSection.title != '' && (
                                            <h3 className="gx-mt-3">
                                                {singleCameraSection.required && (
                                                    <span
                                                        style={{ color: 'red' }}
                                                    >
                                                        {' '}
                                                        *{' '}
                                                    </span>
                                                )}
                                                {singleCameraSection.title}
                                                <hr className="gx-bg-dark"></hr>
                                            </h3>
                                        )}
                                        <CameraInput
                                            required={
                                                singleCameraSection.required
                                            }
                                            authToken={http_utils.getAuthToken()}
                                            prefixDomain={http_utils.getCDNDomain()}
                                            initialFiles={
                                                editMode
                                                    ? getInitialValues()
                                                          .camera_files?.[
                                                          singleCameraSection
                                                              .key
                                                      ]
                                                    : []
                                            }
                                            onFilesChanged={(files) => {
                                                onCameraFilesChanged(
                                                    singleCameraSection.key,
                                                    files
                                                );
                                            }}
                                            onReadyStatusChange={(isReady) => {
                                                onCameraFileUploaderReadyChange(
                                                    singleCameraSection.key,
                                                    isReady
                                                );
                                            }}
                                            readOnly={
                                                singleCameraSection.disabled
                                            }
                                        />
                                    </Col>
                                )
                            )}
                            {fileSections.map((singleFileSection, index) => (
                                <Col
                                    xs={24}
                                    md={24}
                                    className="gx-pl-0"
                                    key={singleFileSection.key}
                                >
                                    {singleFileSection.title != '' && (
                                        <h3 className="gx-mt-3">
                                            {singleFileSection.required && (
                                                <span style={{ color: 'red' }}>
                                                    {' '}
                                                    *{' '}
                                                </span>
                                            )}
                                            {singleFileSection.title}
                                            <hr className="gx-bg-dark"></hr>
                                        </h3>
                                    )}
                                    <Form.Item name={'file_uploads'}>
                                        <S3Uploader
                                            // className="gx-w-50"
                                            // demoMode
                                            required={
                                                singleFileSection.required
                                            }
                                            disabled={
                                                singleFileSection.disabled
                                            }
                                            maxColSpan={6}
                                            authToken={http_utils.getAuthToken()}
                                            prefixDomain={http_utils.getCDNDomain()}
                                            totalFiles={
                                                getInitialValues()
                                                    ?.attachments?.[
                                                    singleFileSection.key
                                                ]
                                            }
                                            onFilesChanged={(files) => {
                                                onFilesChanged(
                                                    singleFileSection.key,
                                                    files
                                                );
                                            }}
                                            onReadyStatusChanged={(isReady) => {
                                                onFileUploaderReadyChange(
                                                    singleFileSection.key,
                                                    isReady
                                                );
                                            }}
                                            initialFiles={
                                                editMode
                                                    ? getInitialValues()
                                                          .attachments?.[
                                                          singleFileSection.key
                                                      ]
                                                    : []
                                            }
                                            compConfig={{
                                                name: 'manage-sku-attachment',
                                            }}
                                            customPreviewHeight="100%"
                                            customFileIconMaxWidth="65px"
                                        />
                                    </Form.Item>
                                </Col>
                            ))}
                            <FormBuilder
                                form={form}
                                meta={getActiveStatusMeta()}
                                disabled={readOnly}
                            ></FormBuilder>

                            {editMode && (
                                <Alert
                                    description={getDescriptionFrAlert()}
                                    type={
                                        viewData?.is_active
                                            ? 'success'
                                            : 'error'
                                    }
                                />
                            )}
                            <Button
                                type="primary"
                                htmlType="submit"
                                disabled={isFormSubmitting || readOnly}
                            >
                                {editMode ? 'Save' : 'Add Product'}
                            </Button>
                            {error ? (
                                <p className="gx-text-red">{error}</p>
                            ) : null}
                        </Form>
                    </Col>
                </Row>
            )}
        </Modal>
    ) : (
        <></>
    );
};

export default ItemEditor;
