// AdminViewMenu.test.js

import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import AdminViewMenu from './AdminViewMenu'; // Adjust the path as necessary
import { useSelector } from 'react-redux';
import { MemoryRouter } from 'react-router-dom';
import ConfigHelpers from '../../util/ConfigHelpers';

// Partially mock react-redux, retaining all exports except useSelector
jest.mock('react-redux', () => ({
    ...jest.requireActual('react-redux'),
    useSelector: jest.fn(),
}));

// Mock ConfigHelpers with all necessary functions
jest.mock('../../util/ConfigHelpers', () => ({
    __esModule: true,
    default: {
        isOwner: jest.fn(),
        isServiceProvider: jest.fn(),
        doesUserHasOneNonOnfieldRole: jest.fn(),
        isUserOnfield: jest.fn(),
        orgHasSbtskType: jest.fn(),
        hasAccessToJobOffers: jest.fn(),
        isProductionEnv: jest.fn(),
        getManageInventoryRights: jest.fn(),
        getStockTransferRights: jest.fn(),
        hasOnfieldAppAccess: jest.fn(),
        isUserAdmin: jest.fn(),
        getSrvcRoute: jest.fn(),
        getUserDetailsInLocalStorage: jest.fn(),
        getUserAuthToken: jest.fn(),
    },
}));

// Mock IntlMessages
jest.mock('../../util/IntlMessages', () => ({ id }) => <span>{id}</span>);

// Mock other dependencies if necessary
jest.mock('../../components/WIFY/CaptureLoginAttendance', () => () => (
    <div>CaptureLoginAttendance</div>
));

// Disable console logs to keep test output clean
beforeAll(() => {
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'group').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
});

afterAll(() => {
    jest.restoreAllMocks();
});

// Helper function to render the component with necessary wrappers
const renderComponent = () => {
    render(
        <MemoryRouter>
            <AdminViewMenu />
        </MemoryRouter>
    );
};

// Define mock return values for ConfigHelpers.getUserDetailsInLocalStorage and getUserAuthToken
const mockUserDetails = {
    userId: 'admin123',
    name: 'Admin User',
    roles: ['admin'],
};

const mockAuthToken = 'mocked-admin-auth-token';

// Before each test, set default mock implementations
beforeEach(() => {
    // Reset all mock implementations before each test
    jest.clearAllMocks();

    ConfigHelpers.getUserDetailsInLocalStorage.mockReturnValue(mockUserDetails);
    ConfigHelpers.getUserAuthToken.mockReturnValue(mockAuthToken);

    // Provide default return values for rights functions
    ConfigHelpers.getManageInventoryRights.mockReturnValue({
        read: jest.fn().mockReturnValue(true),
    });
    ConfigHelpers.getStockTransferRights.mockReturnValue({
        read: jest.fn().mockReturnValue(true),
    });

    // Default implementations for other ConfigHelpers functions
    ConfigHelpers.isOwner.mockReturnValue(false);
    ConfigHelpers.isServiceProvider.mockReturnValue(false);
    ConfigHelpers.doesUserHasOneNonOnfieldRole.mockReturnValue(true);
    ConfigHelpers.isUserOnfield.mockReturnValue(false);
    ConfigHelpers.hasOnfieldAppAccess.mockReturnValue(false);
    ConfigHelpers.isUserAdmin.mockReturnValue(true); // Assuming admin by default
    ConfigHelpers.getSrvcRoute.mockImplementation((menu_id) => {
        const services = {
            service1: {
                srvc_id: '1',
                srvc_icon: 'icon-service1',
                srvc_title: 'Service One',
            },
            service2: {
                srvc_id: '2',
                srvc_icon: 'icon-service2',
                srvc_title: 'Service Two',
            },
        };
        return services[menu_id];
    });
});

test('renders Dashboard Admin for admin users', () => {
    // Setup mocks specific to this test
    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/dashboard',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin'],
                    access_static_routes: [],
                },
            },
        })
    );

    // Render the component
    renderComponent();

    // Check if Dashboard Admin menu item is present
    const dashboardLink = screen.getByText('Dashboard Admin');
    expect(dashboardLink).toBeInTheDocument();
    expect(dashboardLink.closest('a')).toHaveAttribute('href', '/dashboard');
});

test('renders Users menu item for admin users', () => {
    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/users',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const usersLink = screen.getByText('Users');
    expect(usersLink).toBeInTheDocument();
    expect(usersLink.closest('a')).toHaveAttribute('href', '/users');
});

test('renders Customer and Service Providers menu items when not a service provider', () => {
    ConfigHelpers.isServiceProvider.mockReturnValue(false);

    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/customer',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const customerLink = screen.getByText('Customer');
    expect(customerLink).toBeInTheDocument();
    expect(customerLink.closest('a')).toHaveAttribute('href', '/customer');

    const serviceProvidersLink = screen.getByText('Service Providers');
    expect(serviceProvidersLink).toBeInTheDocument();
    expect(serviceProvidersLink.closest('a')).toHaveAttribute(
        'href',
        '/srvcProviders'
    );
});

test('does not render Customer and Service Providers when user is a service provider', () => {
    ConfigHelpers.isServiceProvider.mockReturnValue(true);

    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/dashboard',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin', 'service_provider'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const customerLink = screen.queryByText('Customer');
    expect(customerLink).not.toBeInTheDocument();

    const serviceProvidersLink = screen.queryByText('Service Providers');
    expect(serviceProvidersLink).not.toBeInTheDocument();
});

test('renders Devl Playground in non-production environments', () => {
    // Set NODE_ENV to 'development'
    process.env.NODE_ENV = 'development';

    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/devlPlayground',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const devPlaygroundLink = screen.getByText('Devl Playground');
    expect(devPlaygroundLink).toBeInTheDocument();
    expect(devPlaygroundLink.closest('a')).toHaveAttribute(
        'href',
        '/devlPlayground'
    );
});

test('does not render Devl Playground in production environments', () => {
    // Set NODE_ENV to 'production'
    process.env.NODE_ENV = 'production';

    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/dashboard',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const devPlaygroundLink = screen.queryByText('Devl Playground');
    expect(devPlaygroundLink).not.toBeInTheDocument();
});

test('renders Setup menu group with User configuration and Service configuration submenus', () => {
    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/setup/user-config/roles',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const setupGroup = screen.getByText('Setup');
    expect(setupGroup).toBeInTheDocument();

    const userConfigSubMenu = screen.getByText('User configuration');
    expect(userConfigSubMenu).toBeInTheDocument();

    const serviceConfigSubMenu = screen.getByText('Service configuration');
    expect(serviceConfigSubMenu).toBeInTheDocument();
});

test('renders Zones in User configuration submenu when not a service provider', () => {
    ConfigHelpers.isServiceProvider.mockReturnValue(false);

    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/setup/user-config/zones',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const zonesLink = screen.getByText('Zones');
    expect(zonesLink).toBeInTheDocument();
    expect(zonesLink.closest('a')).toHaveAttribute(
        'href',
        '/setup/user-config/zones'
    );
});

test('does not render Zones in User configuration submenu when user is a service provider', () => {
    ConfigHelpers.isServiceProvider.mockReturnValue(true);

    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/setup/user-config/zones',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin', 'service_provider'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const zonesLink = screen.queryByText('Zones');
    expect(zonesLink).not.toBeInTheDocument();
});

test('renders Service configuration submenu items correctly', () => {
    ConfigHelpers.isServiceProvider.mockReturnValue(true);

    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/setup/srvc-req/types',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin', 'service_provider'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const serviceTypesLink = screen.getByText('Service types');
    expect(serviceTypesLink).toBeInTheDocument();
    expect(serviceTypesLink.closest('a')).toHaveAttribute(
        'href',
        '/setup/srvc-req/types'
    );

    const subtaskTypesLink = screen.getByText('Subtask types');
    expect(subtaskTypesLink).toBeInTheDocument();
    expect(subtaskTypesLink.closest('a')).toHaveAttribute(
        'href',
        '/setup/srvc-req/sub-tasks-types'
    );

    const statusGroupsLink = screen.getByText('Status groups');
    expect(statusGroupsLink).toBeInTheDocument();
    expect(statusGroupsLink.closest('a')).toHaveAttribute(
        'href',
        '/setup/srvc-req/status-group'
    );

    const verticalsLink = screen.getByText('Verticals');
    expect(verticalsLink).toBeInTheDocument();
    expect(verticalsLink.closest('a')).toHaveAttribute(
        'href',
        '/setup/srvc-req/service-provider-fields'
    );
});

test('does not render Service configuration submenu items for non-service providers', () => {
    ConfigHelpers.isServiceProvider.mockReturnValue(false);

    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/setup/srvc-req/types',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const verticalsLink = screen.queryByText('Verticals');
    expect(verticalsLink).not.toBeInTheDocument();
});

test('renders Automate Deployment submenu with all items for service providers', () => {
    ConfigHelpers.isServiceProvider.mockReturnValue(true);

    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/setup/automation-deployment/pool-view',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin', 'service_provider'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const poolViewLink = screen.getByText('Pool View');
    expect(poolViewLink).toBeInTheDocument();
    expect(poolViewLink.closest('a')).toHaveAttribute(
        'href',
        '/setup/automation-deployment/pool-view'
    );

    const pulseTrackerLink = screen.getByText('Pulse Tracker');
    expect(pulseTrackerLink).toBeInTheDocument();
    expect(pulseTrackerLink.closest('a')).toHaveAttribute(
        'href',
        '/setup/automation-deployment/pulse-tracker'
    );

    const lambdaBasedLink = screen.getByText('Lambda based');
    expect(lambdaBasedLink).toBeInTheDocument();
    expect(lambdaBasedLink.closest('a')).toHaveAttribute(
        'href',
        '/setup/automation-deployment/lambda-based'
    );

    const jobBroadcastsLink = screen.getByText('JobBroadcasts');
    expect(jobBroadcastsLink).toBeInTheDocument();
    expect(jobBroadcastsLink.closest('a')).toHaveAttribute(
        'href',
        '/setup/automation-deployment/job-broadcast'
    );

    const executionMasterLink = screen.getByText('Execution Master');
    expect(executionMasterLink).toBeInTheDocument();
    expect(executionMasterLink.closest('a')).toHaveAttribute(
        'href',
        '/setup/automation-deployment/execution-master'
    );
});

test('does not render Lambda based and JobBroadcasts for non-service providers', () => {
    ConfigHelpers.isServiceProvider.mockReturnValue(false);

    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/setup/automation-deployment/pool-view',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const lambdaBasedLink = screen.queryByText('Lambda based');
    expect(lambdaBasedLink).not.toBeInTheDocument();

    const jobBroadcastsLink = screen.queryByText('JobBroadcasts');
    expect(jobBroadcastsLink).not.toBeInTheDocument();
});

test('renders Rate Config submenu with Rate Card for service providers', () => {
    ConfigHelpers.isServiceProvider.mockReturnValue(true);

    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/setup/rate-config/rate-card',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin', 'service_provider'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const rateCardLink = screen.getByText('Rate Card');
    expect(rateCardLink).toBeInTheDocument();
    expect(rateCardLink.closest('a')).toHaveAttribute(
        'href',
        '/setup/rate-config/rate-card'
    );
});

test('does not render Rate Card in Rate Config submenu for non-service providers', () => {
    ConfigHelpers.isServiceProvider.mockReturnValue(false);

    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/setup/rate-config/billing-fields',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const rateCardLink = screen.queryByText('Rate Card');
    expect(rateCardLink).not.toBeInTheDocument();
});

test('renders Inventory Config submenu when not a service provider', () => {
    ConfigHelpers.isServiceProvider.mockReturnValue(false);

    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/setup/inventory-config/manage-custom-fields',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const inventoryConfigSubMenu = screen.getByText('Inventory Config');
    expect(inventoryConfigSubMenu).toBeInTheDocument();

    const customFieldsLink = screen.getByText('Custom Fields');
    expect(customFieldsLink).toBeInTheDocument();
    expect(customFieldsLink.closest('a')).toHaveAttribute(
        'href',
        '/setup/inventory-config/manage-custom-fields'
    );
});

test('does not render Inventory Config submenu for service providers', () => {
    ConfigHelpers.isServiceProvider.mockReturnValue(true);

    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/setup/inventory-config/manage-custom-fields',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin', 'service_provider'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const inventoryConfigSubMenu = screen.queryByText('Inventory Config');
    expect(inventoryConfigSubMenu).not.toBeInTheDocument();
});

test('renders API Docs link', () => {
    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'lite',
            },
            common: {
                pathname: '/api-docs',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const apiDocsLink = screen.getByText('API');
    expect(apiDocsLink).toBeInTheDocument();
    expect(apiDocsLink.closest('a')).toHaveAttribute('href', '/api-docs');
});

test('renders User view (toggle-role) link', () => {
    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/toggle-role',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const userViewLink = screen.getByText('User view');
    expect(userViewLink).toBeInTheDocument();
    expect(userViewLink.closest('a')).toHaveAttribute('href', '/toggle-role');
});

test('renders Rating Templates link', () => {
    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/rating/rating-templates',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const ratingTemplatesLink = screen.getByText('Rating Templates');
    expect(ratingTemplatesLink).toBeInTheDocument();
    expect(ratingTemplatesLink.closest('a')).toHaveAttribute(
        'href',
        '/rating/rating-templates'
    );
});

test('renders Settings link', () => {
    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/setup/srvc-req/settings',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const settingsLink = screen.getByText('Settings');
    expect(settingsLink).toBeInTheDocument();
    expect(settingsLink.closest('a')).toHaveAttribute(
        'href',
        '/setup/srvc-req/settings'
    );
});

test('renders Rate Config submenu correctly based on service provider status', () => {
    // Service provider
    ConfigHelpers.isServiceProvider.mockReturnValue(true);

    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/setup/rate-config/rate-card',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {
                        srvc_list: ['service1', 'service2'],
                    },
                    roles: ['admin', 'service_provider'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const rateCardLink = screen.getByText('Rate Card');
    expect(rateCardLink).toBeInTheDocument();
    expect(rateCardLink.closest('a')).toHaveAttribute(
        'href',
        '/setup/rate-config/rate-card'
    );
});

test('does not render Service configuration submenu items based on empty srvc_list', () => {
    ConfigHelpers.isServiceProvider.mockReturnValue(false);

    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/setup/srvc-req/types',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {
                        srvc_list: [],
                    },
                    roles: ['admin'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const serviceTypesLink = screen.getByText('Service types');
    expect(serviceTypesLink).toBeInTheDocument();

    // Depending on the component's logic, adjust expectations
    // For example, if srvc_list is empty, perhaps some links are not rendered
    // Currently, we only check that 'Service types' is rendered
});

test('navigates correctly when a submenu item is clicked', async () => {
    useSelector.mockImplementation((selector) =>
        selector({
            settings: {
                navStyle: 'NAV_STYLE_DEFAULT',
                themeType: 'dark',
            },
            common: {
                pathname: '/setup/user-config/roles',
            },
            auth: {
                authUser: {
                    access_service_routes: [],
                    config_data: {},
                    roles: ['admin'],
                    access_static_routes: [],
                },
            },
        })
    );

    renderComponent();

    const userConfigSubMenu = screen.getByText('User configuration');
    expect(userConfigSubMenu).toBeInTheDocument();

    // Open the User configuration submenu
    userEvent.click(userConfigSubMenu);

    const rolesLink = await screen.findByText('Roles');
    expect(rolesLink).toBeInTheDocument();
    expect(rolesLink.closest('a')).toHaveAttribute(
        'href',
        '/setup/user-config/roles'
    );
});
